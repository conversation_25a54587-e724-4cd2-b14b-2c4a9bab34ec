{"name": "guidant", "version": "0.1.0", "description": "AI Agent Workflow Orchestrator - Guide AI assistants through systematic development processes", "main": "index.js", "type": "module", "bin": {"guidant": "bin/guidant.js", "guidant-mcp": "mcp-server/server.js"}, "scripts": {"test": "GUIDANT_TEST_MODE=true bun test", "test:watch": "GUIDANT_TEST_MODE=true bun test --watch", "test:coverage": "GUIDANT_TEST_MODE=true bun test --coverage", "test:ui": "GUIDANT_TEST_MODE=true bun run src/ui/v2/test-ui.js", "mcp-server": "bun run mcp-server/server.js", "dev": "bun run scripts/dev.js", "build": "echo 'Build complete'", "start": "bun run index.js", "dashboard": "bun run index.js dashboard", "live": "bun run index.js live", "interactive": "bun run index.js interactive", "format": "biome format . --write", "format:check": "biome format .", "lint": "biome lint .", "lint:fix": "biome lint . --write", "config:check": "bun run src/config/health-check.js", "config:diagnose": "bun run diagnose-config-issues.js"}, "keywords": ["ai", "workflow", "orchestration", "claude", "development", "automation", "mcp", "agent", "guidance"], "author": "Guidant Team", "license": "MIT", "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/perplexity": "^1.1.9", "@anthropic-ai/sdk": "^0.39.0", "@openrouter/ai-sdk-provider": "^0.7.1", "ai": "^4.3.16", "boxen": "^8.0.1", "chalk": "^5.4.1", "chokidar": "^4.0.3", "cli-boxes": "^4.0.1", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^5.1.0", "fastmcp": "^1.20.5", "figlet": "^1.8.1", "fuse.js": "^7.1.0", "glob": "^11.0.2", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "ink": "^6.0.0", "ink-spinner": "^5.0.0", "ink-table": "^3.1.0", "ink-text-input": "^6.0.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "ora": "^8.2.0", "react": "^19.1.0", "task-master-ai": "^0.16.2", "uuid": "^11.1.0", "zod": "^3.23.8"}, "engines": {"bun": ">=1.0.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "execa": "^9.6.0", "ink-testing-library": "^4.0.0", "mock-fs": "^5.5.0"}}