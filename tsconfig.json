{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "jsx": "react-jsx", "jsxImportSource": "react", "strict": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/ui/ink/components/*"], "@cli/*": ["src/cli/*"], "@mcp/*": ["mcp-server/src/*"]}}, "include": ["src/**/*", "mcp-server/src/**/*", "index.js", "bin/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "build", "legacy-context", "legacy-tasks"]}